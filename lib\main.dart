import 'package:flutter/material.dart';

void main() {
  runApp(const IMCApp());
}

class IMCApp extends StatelessWidget {
  const IMCApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Calculadora de IMC',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const IMCCalculator(),
    );
  }
}

class IMCCalculator extends StatefulWidget {
  const IMCCalculator({super.key});

  @override
  State<IMCCalculator> createState() => _IMCCalculatorState();
}

class _IMCCalculatorState extends State<IMCCalculator> {
  final TextEditingController _pesoController = TextEditingController();
  final TextEditingController _alturaController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  double? _imc;
  String _classificacao = '';
  Color _corClassificacao = Colors.black;

  void _calcularIMC() {
    if (_formKey.currentState!.validate()) {
      final double peso = double.parse(
        _pesoController.text.replaceAll(',', '.'),
      );
      final double altura = double.parse(
        _alturaController.text.replaceAll(',', '.'),
      );

      setState(() {
        _imc = peso / (altura * altura);
        _classificarIMC(_imc!);
      });
    }
  }

  void _classificarIMC(double imc) {
    if (imc < 18.5) {
      _classificacao = 'Abaixo do peso';
      _corClassificacao = Colors.blue;
    } else if (imc < 25) {
      _classificacao = 'Peso normal';
      _corClassificacao = Colors.green;
    } else if (imc < 30) {
      _classificacao = 'Sobrepeso';
      _corClassificacao = Colors.orange;
    } else if (imc < 35) {
      _classificacao = 'Obesidade grau I';
      _corClassificacao = Colors.red;
    } else if (imc < 40) {
      _classificacao = 'Obesidade grau II';
      _corClassificacao = Colors.red[700]!;
    } else {
      _classificacao = 'Obesidade grau III';
      _corClassificacao = Colors.red[900]!;
    }
  }

  void _limparCampos() {
    setState(() {
      _pesoController.clear();
      _alturaController.clear();
      _imc = null;
      _classificacao = '';
    });
  }

  String? _validarPeso(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, insira seu peso';
    }
    final peso = double.tryParse(value.replaceAll(',', '.'));
    if (peso == null || peso <= 0) {
      return 'Por favor, insira um peso válido';
    }
    if (peso > 500) {
      return 'Por favor, insira um peso realista';
    }
    return null;
  }

  String? _validarAltura(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, insira sua altura';
    }
    final altura = double.tryParse(value.replaceAll(',', '.'));
    if (altura == null || altura <= 0) {
      return 'Por favor, insira uma altura válida';
    }
    if (altura > 3) {
      return 'Por favor, insira a altura em metros (ex: 1.75)';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Calculadora de IMC'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 20),
              const Icon(Icons.monitor_weight, size: 80, color: Colors.blue),
              const SizedBox(height: 20),
              const Text(
                'Calcule seu Índice de Massa Corporal',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 30),
              TextFormField(
                controller: _pesoController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Peso (kg)',
                  hintText: 'Ex: 70.5',
                  prefixIcon: Icon(Icons.monitor_weight_outlined),
                  border: OutlineInputBorder(),
                ),
                validator: _validarPeso,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _alturaController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Altura (m)',
                  hintText: 'Ex: 1.75',
                  prefixIcon: Icon(Icons.height),
                  border: OutlineInputBorder(),
                ),
                validator: _validarAltura,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _calcularIMC,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text(
                        'Calcular IMC',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _limparCampos,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: 20,
                      ),
                      backgroundColor: Colors.grey[300],
                      foregroundColor: Colors.black87,
                    ),
                    child: const Icon(Icons.clear),
                  ),
                ],
              ),
              const SizedBox(height: 30),
              if (_imc != null) ...[
                Card(
                  elevation: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        const Text(
                          'Resultado',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'IMC: ${_imc!.toStringAsFixed(1)}',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _classificacao,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: _corClassificacao,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                const Card(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Classificação do IMC:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text('• Abaixo de 18,5: Abaixo do peso'),
                        Text('• 18,5 - 24,9: Peso normal'),
                        Text('• 25,0 - 29,9: Sobrepeso'),
                        Text('• 30,0 - 34,9: Obesidade grau I'),
                        Text('• 35,0 - 39,9: Obesidade grau II'),
                        Text('• Acima de 40,0: Obesidade grau III'),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _pesoController.dispose();
    _alturaController.dispose();
    super.dispose();
  }
}
