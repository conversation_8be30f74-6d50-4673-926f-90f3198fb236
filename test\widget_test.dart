// Testes para a aplicação de calculadora de IMC
//
// Para realizar interações com widgets nos testes, use o WidgetTester
// do pacote flutter_test. Por exemplo, você pode enviar gestos de toque e rolagem.
// Você também pode usar WidgetTester para encontrar widgets filhos na árvore de widgets,
// ler texto e verificar se os valores das propriedades dos widgets estão corretos.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:app_002/main.dart';

void main() {
  testWidgets('IMC Calculator smoke test', (WidgetTester tester) async {
    // Constrói nossa aplicação e dispara um frame.
    await tester.pumpWidget(const IMCApp());

    // Verifica se o título da aplicação está presente.
    expect(find.text('Calculadora de IMC'), findsOneWidget);

    // Verifica se os campos de entrada estão presentes.
    expect(find.byType(TextFormField), findsNWidgets(2));

    // Verifica se o botão de calcular está presente.
    expect(find.text('Calcular IMC'), findsOneWidget);
  });

  testWidgets('IMC calculation test', (WidgetTester tester) async {
    // Constrói nossa aplicação e dispara um frame.
    await tester.pumpWidget(const IMCApp());

    // Encontra os campos de entrada.
    final pesoField = find.byType(TextFormField).first;
    final alturaField = find.byType(TextFormField).last;
    final calcularButton = find.text('Calcular IMC');

    // Insere valores nos campos.
    await tester.enterText(pesoField, '70');
    await tester.enterText(alturaField, '1.75');

    // Toca no botão calcular.
    await tester.tap(calcularButton);
    await tester.pump();

    // Verifica se o resultado foi calculado e exibido.
    expect(find.text('Resultado'), findsOneWidget);
    expect(find.text('IMC: 22.9'), findsOneWidget);
    expect(find.text('Peso normal'), findsOneWidget);
  });

  testWidgets('Clear fields test', (WidgetTester tester) async {
    // Constrói nossa aplicação e dispara um frame.
    await tester.pumpWidget(const IMCApp());

    // Encontra os campos de entrada e botões.
    final pesoField = find.byType(TextFormField).first;
    final alturaField = find.byType(TextFormField).last;
    final calcularButton = find.text('Calcular IMC');
    final limparButton = find.byIcon(Icons.clear);

    // Insere valores nos campos.
    await tester.enterText(pesoField, '70');
    await tester.enterText(alturaField, '1.75');

    // Calcula o IMC.
    await tester.tap(calcularButton);
    await tester.pump();

    // Verifica se o resultado está presente.
    expect(find.text('Resultado'), findsOneWidget);

    // Limpa os campos.
    await tester.tap(limparButton);
    await tester.pump();

    // Verifica se os campos foram limpos e o resultado removido.
    expect(find.text('Resultado'), findsNothing);
  });
}
